package com.technews.app

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.webkit.*
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.technews.app.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private val techNewsUrl = "https://tech-news.io"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupWebView()
        setupSwipeRefresh()
        setupErrorLayout()

        // Check network and load the tech-news.io website
        loadWebsite()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        val webView = binding.webView
        val webSettings = webView.settings
        
        // Enable JavaScript (required for modern websites)
        webSettings.javaScriptEnabled = true
        
        // Enable DOM storage
        webSettings.domStorageEnabled = true
        
        // Enable local storage
        webSettings.databaseEnabled = true
        
        // Enable app cache (deprecated in API 33, but keeping for compatibility)
        @Suppress("DEPRECATION")
        webSettings.setAppCacheEnabled(true)
        
        // Set user agent to ensure compatibility
        webSettings.userAgentString = webSettings.userAgentString + " TechNewsApp/1.0"
        
        // Enable zoom controls
        webSettings.setSupportZoom(true)
        webSettings.builtInZoomControls = true
        webSettings.displayZoomControls = false
        
        // Enable responsive design
        webSettings.useWideViewPort = true
        webSettings.loadWithOverviewMode = true
        
        // Mixed content mode for HTTPS sites with HTTP resources
        webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
        
        // Set WebViewClient to handle page navigation
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                val url = request?.url?.toString()
                return if (url != null && url.startsWith("https://tech-news.io")) {
                    // Stay within the app for tech-news.io URLs
                    false
                } else {
                    // For external URLs, you might want to open in external browser
                    // For now, we'll allow them in the WebView
                    false
                }
            }
            
            override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                super.onPageStarted(view, url, favicon)
                showProgressBar()
            }
            
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                hideProgressBar()
                binding.swipeRefreshLayout.isRefreshing = false
            }
            
            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                hideProgressBar()
                binding.swipeRefreshLayout.isRefreshing = false

                if (request?.isForMainFrame == true) {
                    val errorDescription = error?.description?.toString() ?: "Unknown error"
                    showError("Failed to load page", errorDescription)
                }
            }
            
            override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: android.net.http.SslError?) {
                // For production, you should handle SSL errors more carefully
                // For now, we'll proceed to allow the connection
                handler?.proceed()
            }
        }
        
        // Set WebChromeClient to handle progress and title updates
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                updateProgress(newProgress)
            }
            
            override fun onReceivedTitle(view: WebView?, title: String?) {
                super.onReceivedTitle(view, title)
                supportActionBar?.title = title ?: getString(R.string.app_name)
            }
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setOnRefreshListener {
            loadWebsite()
        }

        binding.swipeRefreshLayout.setColorSchemeResources(
            R.color.tech_blue,
            R.color.tech_blue_dark
        )
    }

    private fun setupErrorLayout() {
        val retryButton = binding.errorLayout.root.findViewById<Button>(R.id.retryButton)
        retryButton.setOnClickListener {
            loadWebsite()
        }
    }

    private fun loadWebsite() {
        if (NetworkUtils.isNetworkAvailable(this)) {
            showWebView()
            binding.webView.loadUrl(techNewsUrl)
        } else {
            showError("No internet connection", "Please check your network connection and try again.")
        }
    }
    
    private fun showProgressBar() {
        binding.progressBar.visibility = View.VISIBLE
    }
    
    private fun hideProgressBar() {
        binding.progressBar.visibility = View.GONE
    }
    
    private fun updateProgress(progress: Int) {
        binding.progressBar.progress = progress
        if (progress == 100) {
            hideProgressBar()
        } else {
            showProgressBar()
        }
    }
    
    private fun showWebView() {
        binding.swipeRefreshLayout.visibility = View.VISIBLE
        binding.errorLayout.root.visibility = View.GONE
    }

    private fun showError(title: String, message: String) {
        binding.swipeRefreshLayout.visibility = View.GONE
        binding.errorLayout.root.visibility = View.VISIBLE
        binding.swipeRefreshLayout.isRefreshing = false

        val errorTitle = binding.errorLayout.root.findViewById<TextView>(R.id.errorTitle)
        val errorMessage = binding.errorLayout.root.findViewById<TextView>(R.id.errorMessage)

        errorTitle.text = title
        errorMessage.text = message
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
    
    override fun onBackPressed() {
        if (binding.webView.canGoBack()) {
            binding.webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_refresh -> {
                loadWebsite()
                true
            }
            R.id.action_home -> {
                loadWebsite()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onDestroy() {
        binding.webView.destroy()
        super.onDestroy()
    }
}
