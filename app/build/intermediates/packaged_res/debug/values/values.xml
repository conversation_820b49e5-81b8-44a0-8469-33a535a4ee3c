<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="tech_blue">#FF1976D2</color>
    <color name="tech_blue_dark">#FF0D47A1</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">Tech News</string>
    <string name="error_loading">Error loading page</string>
    <string name="loading">Loading...</string>
    <string name="no_internet">No internet connection</string>
    <string name="refresh">Refresh</string>
    <string name="retry">Retry</string>
    <style name="Theme.TechNewsApp" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/tech_blue</item>
        <item name="colorPrimaryVariant">@color/tech_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>