<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="32dp">

    <ImageView
        android:layout_width="96dp"
        android:layout_height="96dp"
        android:layout_marginBottom="24dp"
        android:src="@drawable/ic_error"
        android:contentDescription="Error icon" />

    <TextView
        android:id="@+id/errorTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/error_loading"
        android:textAppearance="?attr/textAppearanceHeadline6"
        android:textColor="?attr/colorOnSurface" />

    <TextView
        android:id="@+id/errorMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:gravity="center"
        android:text="@string/no_internet"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorOnSurfaceVariant" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/retryButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/retry" />

</LinearLayout>
