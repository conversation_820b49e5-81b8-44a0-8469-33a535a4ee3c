<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res"/><source path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res"><file name="ic_refresh" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/drawable/ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_home" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/drawable/ic_home.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/drawable/ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_error" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/drawable/ic_error.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/drawable/ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="error_layout" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/layout/error_layout.xml" qualifiers="" type="layout"/><file name="activity_main" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/layout/activity_main.xml" qualifiers="" type="layout"/><file path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/values/colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="tech_blue">#FF1976D2</color><color name="tech_blue_dark">#FF0D47A1</color></file><file path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/values/themes.xml" qualifiers=""><style name="Theme.TechNewsApp" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/tech_blue</item>
        <item name="colorPrimaryVariant">@color/tech_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">Tech News</string><string name="loading">Loading...</string><string name="error_loading">Error loading page</string><string name="no_internet">No internet connection</string><string name="retry">Retry</string><string name="refresh">Refresh</string></file><file name="backup_rules" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/xml/backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/xml/data_extraction_rules.xml" qualifiers="" type="xml"/><file name="main_menu" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/menu/main_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/></source><source path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/build/generated/res/rs/debug"/><source path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>