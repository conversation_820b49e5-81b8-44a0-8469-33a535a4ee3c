{"logs": [{"outputFile": "com.technews.app-mergeDebugResources-28:/values-tr/values-tr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/res/values-tr/values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "7529", "endColumns": "100", "endOffsets": "7625"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,7449", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,7524"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/res/values-tr/values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,342,438,550,632,696,787,864,925,1016,1079,1138,1207,1270,1324,1432,1490,1552,1606,1679,1800,1884,1975,2115,2192,2268,2355,2408,2462,2528,2598,2675,2758,2829,2904,2982,3053,3138,3227,3322,3415,3487,3559,3655,3707,3774,3858,3948,4010,4074,4137,4231,4327,4416,4513,4570,4628", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,86,52,53,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,56,57,78", "endOffsets": "260,337,433,545,627,691,782,859,920,1011,1074,1133,1202,1265,1319,1427,1485,1547,1601,1674,1795,1879,1970,2110,2187,2263,2350,2403,2457,2523,2593,2670,2753,2824,2899,2977,3048,3133,3222,3317,3410,3482,3554,3650,3702,3769,3853,3943,4005,4069,4132,4226,4322,4411,4508,4565,4623,4702"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3084,3180,3292,3374,3438,3529,3606,3667,3758,3821,3880,3949,4012,4066,4174,4232,4294,4348,4421,4542,4626,4717,4857,4934,5010,5097,5150,5204,5270,5340,5417,5500,5571,5646,5724,5795,5880,5969,6064,6157,6229,6301,6397,6449,6516,6600,6690,6752,6816,6879,6973,7069,7158,7255,7312,7370", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,86,52,53,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,56,57,78", "endOffsets": "310,3079,3175,3287,3369,3433,3524,3601,3662,3753,3816,3875,3944,4007,4061,4169,4227,4289,4343,4416,4537,4621,4712,4852,4929,5005,5092,5145,5199,5265,5335,5412,5495,5566,5641,5719,5790,5875,5964,6059,6152,6224,6296,6392,6444,6511,6595,6685,6747,6811,6874,6968,7064,7153,7250,7307,7365,7444"}}]}]}