{"logs": [{"outputFile": "com.technews.app-mergeDebugResources-28:/values-gu/values-gu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/res/values-gu/values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,350,449,569,653,716,807,874,933,1023,1088,1152,1221,1283,1337,1452,1510,1571,1625,1698,1825,1911,1995,2128,2203,2279,2365,2419,2471,2537,2610,2690,2775,2846,2922,3001,3070,3166,3244,3339,3435,3509,3584,3683,3734,3801,3888,3978,4040,4104,4167,4269,4374,4471,4577,4635,4691", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,81,98,119,83,62,90,66,58,89,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,85,53,51,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,57,55,77", "endOffsets": "263,345,444,564,648,711,802,869,928,1018,1083,1147,1216,1278,1332,1447,1505,1566,1620,1693,1820,1906,1990,2123,2198,2274,2360,2414,2466,2532,2605,2685,2770,2841,2917,2996,3065,3161,3239,3334,3430,3504,3579,3678,3729,3796,3883,3973,4035,4099,4162,4264,4369,4466,4572,4630,4686,4764"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3001,3083,3182,3302,3386,3449,3540,3607,3666,3756,3821,3885,3954,4016,4070,4185,4243,4304,4358,4431,4558,4644,4728,4861,4936,5012,5098,5152,5204,5270,5343,5423,5508,5579,5655,5734,5803,5899,5977,6072,6168,6242,6317,6416,6467,6534,6621,6711,6773,6837,6900,7002,7107,7204,7310,7368,7424", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "endColumns": "12,81,98,119,83,62,90,66,58,89,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,85,53,51,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,57,55,77", "endOffsets": "313,3078,3177,3297,3381,3444,3535,3602,3661,3751,3816,3880,3949,4011,4065,4180,4238,4299,4353,4426,4553,4639,4723,4856,4931,5007,5093,5147,5199,5265,5338,5418,5503,5574,5650,5729,5798,5894,5972,6067,6163,6237,6312,6411,6462,6529,6616,6706,6768,6832,6895,6997,7102,7199,7305,7363,7419,7497"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,7502", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,7578"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/res/values-gu/values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "7583", "endColumns": "100", "endOffsets": "7679"}}]}]}