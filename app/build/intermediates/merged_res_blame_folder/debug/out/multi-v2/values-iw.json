{"logs": [{"outputFile": "com.technews.app-mergeDebugResources-28:/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/res/values-iw/values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "93", "startColumns": "4", "startOffsets": "7482", "endColumns": "100", "endOffsets": "7578"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,364,441,534,647,727,792,880,950,1013,1105,1165,1224,1287,1348,1402,1504,1561,1620,1674,1742,1853,1934,2016,2148,2219,2292,2380,2433,2487,2553,2626,2702,2788,2858,2933,3015,3083,3168,3238,3328,3419,3493,3566,3655,3706,3773,3855,3940,4002,4066,4129,4223,4318,4408,4504,4561,4619", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,87,52,53,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,56,57,74", "endOffsets": "359,436,529,642,722,787,875,945,1008,1100,1160,1219,1282,1343,1397,1499,1556,1615,1669,1737,1848,1929,2011,2143,2214,2287,2375,2428,2482,2548,2621,2697,2783,2853,2928,3010,3078,3163,3233,3323,3414,3488,3561,3650,3701,3768,3850,3935,3997,4061,4124,4218,4313,4403,4499,4556,4614,4689"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3147,3240,3353,3433,3498,3586,3656,3719,3811,3871,3930,3993,4054,4108,4210,4267,4326,4380,4448,4559,4640,4722,4854,4925,4998,5086,5139,5193,5259,5332,5408,5494,5564,5639,5721,5789,5874,5944,6034,6125,6199,6272,6361,6412,6479,6561,6646,6708,6772,6835,6929,7024,7114,7210,7267,7325", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,87,52,53,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,56,57,74", "endOffsets": "409,3142,3235,3348,3428,3493,3581,3651,3714,3806,3866,3925,3988,4049,4103,4205,4262,4321,4375,4443,4554,4635,4717,4849,4920,4993,5081,5134,5188,5254,5327,5403,5489,5559,5634,5716,5784,5869,5939,6029,6120,6194,6267,6356,6407,6474,6556,6641,6703,6767,6830,6924,7019,7109,7205,7262,7320,7395"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "414,519,619,727,811,913,1029,1108,1186,1277,1371,1465,1559,1659,1752,1847,1940,2031,2123,2204,2309,2412,2510,2615,2717,2819,2973,7400", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "514,614,722,806,908,1024,1103,1181,1272,1366,1460,1554,1654,1747,1842,1935,2026,2118,2199,2304,2407,2505,2610,2712,2814,2968,3065,7477"}}]}]}