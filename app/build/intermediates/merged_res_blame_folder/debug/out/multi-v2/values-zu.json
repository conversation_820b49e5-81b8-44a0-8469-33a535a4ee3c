{"logs": [{"outputFile": "com.technews.app-mergeDebugResources-28:/values-zu/values-zu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/res/values-zu/values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "7751", "endColumns": "100", "endOffsets": "7847"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,536,648,736,839,954,1033,1110,1201,1294,1389,1483,1583,1676,1771,1865,1956,2049,2130,2234,2337,2435,2542,2649,2754,2911,7669", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "424,531,643,731,834,949,1028,1105,1196,1289,1384,1478,1578,1671,1766,1860,1951,2044,2125,2229,2332,2430,2537,2644,2749,2906,3002,7746"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/res/values-zu/values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,348,460,586,667,734,837,912,975,1067,1132,1199,1271,1343,1397,1518,1577,1641,1695,1772,1904,1989,2070,2219,2306,2389,2481,2537,2595,2661,2733,2810,2901,2981,3060,3135,3214,3304,3377,3471,3568,3642,3715,3814,3869,3937,4025,4114,4176,4240,4303,4412,4517,4620,4729,4789,4851", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,91,55,57,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,59,61,81", "endOffsets": "266,343,455,581,662,729,832,907,970,1062,1127,1194,1266,1338,1392,1513,1572,1636,1690,1767,1899,1984,2065,2214,2301,2384,2476,2532,2590,2656,2728,2805,2896,2976,3055,3130,3209,3299,3372,3466,3563,3637,3710,3809,3864,3932,4020,4109,4171,4235,4298,4407,4512,4615,4724,4784,4846,4928"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3084,3196,3322,3403,3470,3573,3648,3711,3803,3868,3935,4007,4079,4133,4254,4313,4377,4431,4508,4640,4725,4806,4955,5042,5125,5217,5273,5331,5397,5469,5546,5637,5717,5796,5871,5950,6040,6113,6207,6304,6378,6451,6550,6605,6673,6761,6850,6912,6976,7039,7148,7253,7356,7465,7525,7587", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,91,55,57,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,59,61,81", "endOffsets": "316,3079,3191,3317,3398,3465,3568,3643,3706,3798,3863,3930,4002,4074,4128,4249,4308,4372,4426,4503,4635,4720,4801,4950,5037,5120,5212,5268,5326,5392,5464,5541,5632,5712,5791,5866,5945,6035,6108,6202,6299,6373,6446,6545,6600,6668,6756,6845,6907,6971,7034,7143,7248,7351,7460,7520,7582,7664"}}]}]}