{"logs": [{"outputFile": "com.technews.app-mergeDebugResources-28:/values-ne/values-ne.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,344,446,556,643,709,805,871,932,1037,1109,1167,1241,1303,1357,1470,1530,1591,1650,1728,1852,1933,2018,2154,2235,2318,2401,2463,2517,2583,2660,2739,2827,2896,2972,3053,3121,3212,3290,3383,3480,3554,3633,3731,3791,3857,3945,4033,4095,4163,4226,4331,4449,4544,4664,4730,4788", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,82,61,53,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,65,57,83", "endOffsets": "257,339,441,551,638,704,800,866,927,1032,1104,1162,1236,1298,1352,1465,1525,1586,1645,1723,1847,1928,2013,2149,2230,2313,2396,2458,2512,2578,2655,2734,2822,2891,2967,3048,3116,3207,3285,3378,3475,3549,3628,3726,3786,3852,3940,4028,4090,4158,4221,4326,4444,4539,4659,4725,4783,4867"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3072,3154,3256,3366,3453,3519,3615,3681,3742,3847,3919,3977,4051,4113,4167,4280,4340,4401,4460,4538,4662,4743,4828,4964,5045,5128,5211,5273,5327,5393,5470,5549,5637,5706,5782,5863,5931,6022,6100,6193,6290,6364,6443,6541,6601,6667,6755,6843,6905,6973,7036,7141,7259,7354,7474,7540,7598", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,82,61,53,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,65,57,83", "endOffsets": "307,3149,3251,3361,3448,3514,3610,3676,3737,3842,3914,3972,4046,4108,4162,4275,4335,4396,4455,4533,4657,4738,4823,4959,5040,5123,5206,5268,5322,5388,5465,5544,5632,5701,5777,5858,5926,6017,6095,6188,6285,6359,6438,6536,6596,6662,6750,6838,6900,6968,7031,7136,7254,7349,7469,7535,7593,7677"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2454,2567,2677,2794,2961,7682", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2449,2562,2672,2789,2956,3067,7757"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/res/values-ne/values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "7762", "endColumns": "100", "endOffsets": "7858"}}]}]}