<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.technews.app" filePath="app/src/main/res/layout/activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="64" endOffset="51"/></Target><Target id="@+id/errorLayout" tag="layout/activity_main_0" include="error_layout"><Expressions/><location startLine="53" startOffset="4" endLine="62" endOffset="65"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="24" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/swipeRefreshLayout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="26" startOffset="4" endLine="40" endOffset="59"/></Target><Target id="@+id/webView" view="WebView"><Expressions/><location startLine="35" startOffset="8" endLine="38" endOffset="50"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="42" startOffset="4" endLine="51" endOffset="65"/></Target></Targets></Layout>