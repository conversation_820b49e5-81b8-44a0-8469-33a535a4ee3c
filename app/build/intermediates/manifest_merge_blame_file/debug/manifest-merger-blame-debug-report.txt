1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.technews.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:5:5-67
11-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:6:5-79
12-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
15        android:name="com.technews.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.technews.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
19
20    <application
20-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:8:5-28:19
21        android:allowBackup="true"
21-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:14:9-54
30        android:theme="@style/Theme.TechNewsApp"
30-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:15:9-49
31        android:usesCleartextTraffic="true" >
31-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:16:9-44
32        <activity
32-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:19:9-27:20
33            android:name="com.technews.app.MainActivity"
33-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:20:13-41
34            android:exported="true"
34-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:21:13-36
35            android:theme="@style/Theme.TechNewsApp" >
35-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:22:13-53
36            <intent-filter>
36-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:23:13-26:29
37                <action android:name="android.intent.action.MAIN" />
37-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:24:17-69
37-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:24:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:25:17-77
39-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:25:27-74
40            </intent-filter>
41        </activity>
42
43        <provider
43-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
44            android:name="androidx.startup.InitializationProvider"
44-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
45            android:authorities="com.technews.app.androidx-startup"
45-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
46            android:exported="false" >
46-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
47            <meta-data
47-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
48                android:name="androidx.emoji2.text.EmojiCompatInitializer"
48-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
49                android:value="androidx.startup" />
49-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
51                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
51-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
52                android:value="androidx.startup" />
52-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
53        </provider>
54    </application>
55
56</manifest>
