1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.technews.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:5:5-67
11-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:6:5-79
12-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
15        android:name="com.technews.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.technews.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
19
20    <application
20-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:8:5-28:19
21        android:allowBackup="true"
21-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:10:9-65
24        android:extractNativeLibs="false"
25        android:fullBackupContent="@xml/backup_rules"
25-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:11:9-54
26        android:icon="@mipmap/ic_launcher"
26-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:12:9-43
27        android:label="@string/app_name"
27-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:13:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:14:9-54
29        android:theme="@style/Theme.TechNewsApp"
29-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:15:9-49
30        android:usesCleartextTraffic="true" >
30-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:16:9-44
31        <activity
31-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:19:9-27:20
32            android:name="com.technews.app.MainActivity"
32-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:20:13-41
33            android:exported="true"
33-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:21:13-36
34            android:theme="@style/Theme.TechNewsApp" >
34-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:22:13-53
35            <intent-filter>
35-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:23:13-26:29
36                <action android:name="android.intent.action.MAIN" />
36-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:24:17-69
36-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:24:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:25:17-77
38-->/Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:25:27-74
39            </intent-filter>
40        </activity>
41
42        <provider
42-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
43            android:name="androidx.startup.InitializationProvider"
43-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
44            android:authorities="com.technews.app.androidx-startup"
44-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
45            android:exported="false" >
45-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
46            <meta-data
46-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
47                android:name="androidx.emoji2.text.EmojiCompatInitializer"
47-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
48                android:value="androidx.startup" />
48-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
49            <meta-data
49-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
50                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
50-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
51                android:value="androidx.startup" />
51-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
52        </provider>
53    </application>
54
55</manifest>
