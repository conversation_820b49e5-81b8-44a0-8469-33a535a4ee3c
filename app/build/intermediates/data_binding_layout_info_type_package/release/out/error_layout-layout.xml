<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="error_layout" modulePackage="com.technews.app" filePath="app/src/main/res/layout/error_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/error_layout_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="40" endOffset="14"/></Target><Target id="@+id/errorTitle" view="TextView"><Expressions/><location startLine="15" startOffset="4" endLine="22" endOffset="50"/></Target><Target id="@+id/errorMessage" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="32" endOffset="57"/></Target><Target id="@+id/retryButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="34" startOffset="4" endLine="38" endOffset="38"/></Target></Targets></Layout>