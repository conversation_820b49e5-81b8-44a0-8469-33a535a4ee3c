com.technews.app-constraintlayout-2.1.4-0 /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/res
com.technews.app-viewpager2-1.0.0-1 /Users/<USER>/.gradle/caches/transforms-3/17473142177f1aaac34c6335ae7e71fa/transformed/viewpager2-1.0.0/res
com.technews.app-activity-1.5.1-2 /Users/<USER>/.gradle/caches/transforms-3/1b11c718c5849365377a9d875c75c22c/transformed/activity-1.5.1/res
com.technews.app-lifecycle-viewmodel-2.5.1-3 /Users/<USER>/.gradle/caches/transforms-3/205605b76180136cc8f42c99fdbd78ad/transformed/lifecycle-viewmodel-2.5.1/res
com.technews.app-coordinatorlayout-1.1.0-4 /Users/<USER>/.gradle/caches/transforms-3/41f5bc421223c690c51c8e734819e8da/transformed/coordinatorlayout-1.1.0/res
com.technews.app-core-1.9.0-5 /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/res
com.technews.app-fragment-1.3.6-6 /Users/<USER>/.gradle/caches/transforms-3/560ccd516cd6de1b6638828af3eebb71/transformed/fragment-1.3.6/res
com.technews.app-lifecycle-livedata-core-2.5.1-7 /Users/<USER>/.gradle/caches/transforms-3/603b40d55b283007ec925a6b3494c797/transformed/lifecycle-livedata-core-2.5.1/res
com.technews.app-transition-1.2.0-8 /Users/<USER>/.gradle/caches/transforms-3/61017ffc1d01ff413e7d898b997b69c2/transformed/transition-1.2.0/res
com.technews.app-recyclerview-1.1.0-9 /Users/<USER>/.gradle/caches/transforms-3/74257452692e5c2c34a27252223c312c/transformed/recyclerview-1.1.0/res
com.technews.app-lifecycle-process-2.4.1-10 /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/res
com.technews.app-emoji2-views-helper-1.2.0-11 /Users/<USER>/.gradle/caches/transforms-3/96e99f535981952eca70dcea7436f9a8/transformed/emoji2-views-helper-1.2.0/res
com.technews.app-swiperefreshlayout-1.1.0-12 /Users/<USER>/.gradle/caches/transforms-3/b22a6e0b070cb45cd123fc6a8ac6d7ac/transformed/swiperefreshlayout-1.1.0/res
com.technews.app-appcompat-resources-1.5.1-13 /Users/<USER>/.gradle/caches/transforms-3/b305d98b1a86fb88963595cbfebfdfab/transformed/appcompat-resources-1.5.1/res
com.technews.app-startup-runtime-1.1.1-14 /Users/<USER>/.gradle/caches/transforms-3/b905560b2da4be86f30e02660b710b15/transformed/startup-runtime-1.1.1/res
com.technews.app-savedstate-1.2.0-15 /Users/<USER>/.gradle/caches/transforms-3/baa876de43fb379b2b6dab8fbed6b608/transformed/savedstate-1.2.0/res
com.technews.app-appcompat-1.5.1-16 /Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/res
com.technews.app-cardview-1.0.0-17 /Users/<USER>/.gradle/caches/transforms-3/bf7e18ef8301d1d7f6bc63773afcfb9c/transformed/cardview-1.0.0/res
com.technews.app-annotation-experimental-1.3.0-18 /Users/<USER>/.gradle/caches/transforms-3/c15864de81153c1f158ef9d18de9ad3a/transformed/annotation-experimental-1.3.0/res
com.technews.app-lifecycle-viewmodel-savedstate-2.5.1-19 /Users/<USER>/.gradle/caches/transforms-3/dd7cfcb5a58d5491bfcb53900100245d/transformed/lifecycle-viewmodel-savedstate-2.5.1/res
com.technews.app-drawerlayout-1.1.1-20 /Users/<USER>/.gradle/caches/transforms-3/e38ee089648600abdf2b1b34f81c65de/transformed/drawerlayout-1.1.1/res
com.technews.app-core-ktx-1.9.0-21 /Users/<USER>/.gradle/caches/transforms-3/e80bc102ae7b3ad78be9ca567c1ef740/transformed/core-ktx-1.9.0/res
com.technews.app-material-1.7.0-22 /Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/res
com.technews.app-lifecycle-runtime-2.5.1-23 /Users/<USER>/.gradle/caches/transforms-3/f0e7d6d799d6dcb9d78b59a41c74ca7d/transformed/lifecycle-runtime-2.5.1/res
com.technews.app-emoji2-1.2.0-24 /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/res
com.technews.app-pngs-25 /Users/<USER>/Documents/augment-projects/test-webview-an/app/build/generated/res/pngs/debug
com.technews.app-resValues-26 /Users/<USER>/Documents/augment-projects/test-webview-an/app/build/generated/res/resValues/debug
com.technews.app-rs-27 /Users/<USER>/Documents/augment-projects/test-webview-an/app/build/generated/res/rs/debug
com.technews.app-packageDebugResources-28 /Users/<USER>/Documents/augment-projects/test-webview-an/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
com.technews.app-packageDebugResources-29 /Users/<USER>/Documents/augment-projects/test-webview-an/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.technews.app-merged_res-30 /Users/<USER>/Documents/augment-projects/test-webview-an/app/build/intermediates/merged_res/debug
com.technews.app-debug-31 /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/debug/res
com.technews.app-main-32 /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/res
