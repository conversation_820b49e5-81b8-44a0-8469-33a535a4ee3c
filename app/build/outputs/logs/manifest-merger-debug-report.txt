-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
MERGED from [androidx.databinding:viewbinding:7.4.2] /Users/<USER>/.gradle/caches/transforms-3/6b7ca0c285bece8bb9139c95c6c72977/transformed/viewbinding-7.4.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/b305d98b1a86fb88963595cbfebfdfab/transformed/appcompat-resources-1.5.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/17473142177f1aaac34c6335ae7e71fa/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/560ccd516cd6de1b6638828af3eebb71/transformed/fragment-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/1b11c718c5849365377a9d875c75c22c/transformed/activity-1.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/61bcdcb940fbb590286c186b857db46a/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07dd9cf1e5e9d3a0834cbd1953c0cf54/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f0cd2140268ad88a6efad3ac05386ab8/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/205605b76180136cc8f42c99fdbd78ad/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/dd7cfcb5a58d5491bfcb53900100245d/transformed/lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/b22a6e0b070cb45cd123fc6a8ac6d7ac/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/96e99f535981952eca70dcea7436f9a8/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/e38ee089648600abdf2b1b34f81c65de/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/41f5bc421223c690c51c8e734819e8da/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/61017ffc1d01ff413e7d898b997b69c2/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2deddba07ed6cbf9631edbc4999ddc1a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/9c163b3c0f223d55e9867cb5b985a609/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1b0dec12bf907a0ce71737e1bcb2c2d1/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/74257452692e5c2c34a27252223c312c/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/711082ff04b262488c989f0a20113488/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/e80bc102ae7b3ad78be9ca567c1ef740/transformed/core-ktx-1.9.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0fd9d8751b56a2d26f0d29d3b3e9f257/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/f0e7d6d799d6dcb9d78b59a41c74ca7d/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/baa876de43fb379b2b6dab8fbed6b608/transformed/savedstate-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/bf7e18ef8301d1d7f6bc63773afcfb9c/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/14cf0f12229a19f9854ef132476f78e1/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b905560b2da4be86f30e02660b710b15/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/7b2183c3cbd89865b4ee0e8a658ff57b/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/8a642dd31dddf0535e9c303cc38f8bf5/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/603b40d55b283007ec925a6b3494c797/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-3/4ee6b80c7f1b747b930e882ed6e406f7/transformed/core-runtime-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f9b79652572637781f542df77a91d7cc/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/43551e7787504844afc94f958065b3d6/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0ef88f649878603ab3d2e983b9bc7b1/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/c15864de81153c1f158ef9d18de9ad3a/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
	package
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:1-30:12
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:5:5-67
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:6:5-79
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:6:22-76
application
ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:8:5-28:19
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:25:5-35:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b905560b2da4be86f30e02660b710b15/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b905560b2da4be86f30e02660b710b15/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:28:18-86
	android:label
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:13:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:11:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:14:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:17:9-29
	android:icon
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:12:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:9:9-35
	android:theme
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:15:9-49
	android:dataExtractionRules
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:10:9-65
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:16:9-44
activity#com.technews.app.MainActivity
ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:19:9-27:20
	android:exported
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:21:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:22:13-53
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:20:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:23:13-26:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:24:17-69
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:24:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:25:17-77
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml:25:27-74
uses-sdk
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.4.2] /Users/<USER>/.gradle/caches/transforms-3/6b7ca0c285bece8bb9139c95c6c72977/transformed/viewbinding-7.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] /Users/<USER>/.gradle/caches/transforms-3/6b7ca0c285bece8bb9139c95c6c72977/transformed/viewbinding-7.4.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/ec87c3272666b2ab8c11c5c84a34cadd/transformed/material-1.7.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/b305d98b1a86fb88963595cbfebfdfab/transformed/appcompat-resources-1.5.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/b305d98b1a86fb88963595cbfebfdfab/transformed/appcompat-resources-1.5.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/be4501e9f667f66baffcf51f093bb8d1/transformed/appcompat-1.5.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/17473142177f1aaac34c6335ae7e71fa/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/17473142177f1aaac34c6335ae7e71fa/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/560ccd516cd6de1b6638828af3eebb71/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/560ccd516cd6de1b6638828af3eebb71/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/1b11c718c5849365377a9d875c75c22c/transformed/activity-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.1] /Users/<USER>/.gradle/caches/transforms-3/1b11c718c5849365377a9d875c75c22c/transformed/activity-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/61bcdcb940fbb590286c186b857db46a/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/61bcdcb940fbb590286c186b857db46a/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07dd9cf1e5e9d3a0834cbd1953c0cf54/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07dd9cf1e5e9d3a0834cbd1953c0cf54/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f0cd2140268ad88a6efad3ac05386ab8/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f0cd2140268ad88a6efad3ac05386ab8/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/205605b76180136cc8f42c99fdbd78ad/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/205605b76180136cc8f42c99fdbd78ad/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/dd7cfcb5a58d5491bfcb53900100245d/transformed/lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/dd7cfcb5a58d5491bfcb53900100245d/transformed/lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/b22a6e0b070cb45cd123fc6a8ac6d7ac/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/b22a6e0b070cb45cd123fc6a8ac6d7ac/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/96e99f535981952eca70dcea7436f9a8/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/96e99f535981952eca70dcea7436f9a8/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/e38ee089648600abdf2b1b34f81c65de/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/e38ee089648600abdf2b1b34f81c65de/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/41f5bc421223c690c51c8e734819e8da/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/41f5bc421223c690c51c8e734819e8da/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/61017ffc1d01ff413e7d898b997b69c2/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/61017ffc1d01ff413e7d898b997b69c2/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2deddba07ed6cbf9631edbc4999ddc1a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2deddba07ed6cbf9631edbc4999ddc1a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/9c163b3c0f223d55e9867cb5b985a609/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/9c163b3c0f223d55e9867cb5b985a609/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1b0dec12bf907a0ce71737e1bcb2c2d1/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1b0dec12bf907a0ce71737e1bcb2c2d1/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/74257452692e5c2c34a27252223c312c/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/74257452692e5c2c34a27252223c312c/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/711082ff04b262488c989f0a20113488/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/711082ff04b262488c989f0a20113488/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/e80bc102ae7b3ad78be9ca567c1ef740/transformed/core-ktx-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/e80bc102ae7b3ad78be9ca567c1ef740/transformed/core-ktx-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0fd9d8751b56a2d26f0d29d3b3e9f257/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0fd9d8751b56a2d26f0d29d3b3e9f257/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/f0e7d6d799d6dcb9d78b59a41c74ca7d/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/f0e7d6d799d6dcb9d78b59a41c74ca7d/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/baa876de43fb379b2b6dab8fbed6b608/transformed/savedstate-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/baa876de43fb379b2b6dab8fbed6b608/transformed/savedstate-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/bf7e18ef8301d1d7f6bc63773afcfb9c/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/bf7e18ef8301d1d7f6bc63773afcfb9c/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/14cf0f12229a19f9854ef132476f78e1/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/14cf0f12229a19f9854ef132476f78e1/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b905560b2da4be86f30e02660b710b15/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b905560b2da4be86f30e02660b710b15/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/7b2183c3cbd89865b4ee0e8a658ff57b/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/7b2183c3cbd89865b4ee0e8a658ff57b/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/8a642dd31dddf0535e9c303cc38f8bf5/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/8a642dd31dddf0535e9c303cc38f8bf5/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/603b40d55b283007ec925a6b3494c797/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/603b40d55b283007ec925a6b3494c797/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-3/4ee6b80c7f1b747b930e882ed6e406f7/transformed/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-3/4ee6b80c7f1b747b930e882ed6e406f7/transformed/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f9b79652572637781f542df77a91d7cc/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f9b79652572637781f542df77a91d7cc/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/43551e7787504844afc94f958065b3d6/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/43551e7787504844afc94f958065b3d6/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0ef88f649878603ab3d2e983b9bc7b1/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0ef88f649878603ab3d2e983b9bc7b1/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/c15864de81153c1f158ef9d18de9ad3a/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/c15864de81153c1f158ef9d18de9ad3a/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Documents/augment-projects/test-webview-an/app/src/main/AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b905560b2da4be86f30e02660b710b15/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b905560b2da4be86f30e02660b710b15/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/f5bce95adc2736b0739e9bbfa23aa81b/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
permission#com.technews.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
uses-permission#com.technews.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/43eeba27e46c0f9e8270c8e6a59600bf/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/8af27606e6293b3b18c49e9b214e0d7e/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
