// Generated by view binder compiler. Do not edit!
package com.technews.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.technews.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ErrorLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView errorMessage;

  @NonNull
  public final TextView errorTitle;

  @NonNull
  public final MaterialButton retryButton;

  private ErrorLayoutBinding(@NonNull LinearLayout rootView, @NonNull TextView errorMessage,
      @NonNull TextView errorTitle, @NonNull MaterialButton retryButton) {
    this.rootView = rootView;
    this.errorMessage = errorMessage;
    this.errorTitle = errorTitle;
    this.retryButton = retryButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ErrorLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ErrorLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.error_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ErrorLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.errorMessage;
      TextView errorMessage = ViewBindings.findChildViewById(rootView, id);
      if (errorMessage == null) {
        break missingId;
      }

      id = R.id.errorTitle;
      TextView errorTitle = ViewBindings.findChildViewById(rootView, id);
      if (errorTitle == null) {
        break missingId;
      }

      id = R.id.retryButton;
      MaterialButton retryButton = ViewBindings.findChildViewById(rootView, id);
      if (retryButton == null) {
        break missingId;
      }

      return new ErrorLayoutBinding((LinearLayout) rootView, errorMessage, errorTitle, retryButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
